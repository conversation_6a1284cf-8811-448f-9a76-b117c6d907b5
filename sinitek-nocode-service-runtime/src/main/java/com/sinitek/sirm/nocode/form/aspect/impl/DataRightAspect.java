package com.sinitek.sirm.nocode.form.aspect.impl;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.sirm.nocode.form.aspect.ZdAuth;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.support.util.AppRightEl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;
import java.util.Objects;

/**
 * 权限切面
 *
 * <AUTHOR>
 * @version 2025.0328
 * @since v1.0.0.x
 */
@Aspect
@Component
@Slf4j
public class DataRightAspect {
    @Resource
    private IZdPageAuthService pageAuthService;
    @Resource
    private IZdPageFormService pageFormService;
    @Resource
    private AppRightEl appRightEl;

    @Pointcut("@annotation(dataRight)")
    public void aspect(ZdAuth dataRight) {
        // 切面
    }

    @Before(value = "aspect(dataRight)", argNames = "joinPoint,dataRight")
    public void setDataRight(JoinPoint joinPoint, ZdAuth dataRight) {
        Object[] args = joinPoint.getArgs();
        String formCode = findFormCode(args, joinPoint);
        // 表单是否存在
        boolean exists = pageFormService.exists(formCode);
        if (!exists) {
            // 该表单不存在
            throw new BussinessException("3000010");
        }
        // 一定是要发布的表单
        if (!pageFormService.isPublished(formCode)) {
            // 该表单没有发布 todo 到时候再放开。
            //throw new BussinessException("3000011");
        }


        // 看看是不是可以获取到appCode,假如获取到的话，那么该人员拥有表单数据的全部权限。
        String appCode = RequestContext.getAppCode();
        if (StringUtils.isNotBlank(appCode)) {
            // 查看该表单是不是属于这个应用,防止篡权查询表单数据
            boolean b = pageFormService.codeBelongsToAppCode(formCode, appCode);
            if (!b) {
                // 该表单不属于这个应用
                throw new BussinessException("3000009");
            }
            // 这个时候不做权限控制，可以获取所有的数据
            return;
        }
        // 有应用权限的跳过
        boolean hasAppRight = appRightEl.hasAppAuthByPageCode(formCode, CurrentUserFactory.getOrgId());
        if (hasAppRight) {
            // 这个时候不做权限控制，可以获取所有的数据
            return;
        }
        // 获取应用编码
        appCode = appRightEl.getAppCodeByPageCode(formCode);
        // todo 需要判断这个应用是否存在。
        if (dataRight.checkStatus() && !appRightEl.isEnable(appCode)) {
            // 应用已经被禁用！
            log.info("应用被禁用！appCode:{}", appCode);
            throw new BussinessException("3000017");
        }

        OperationAuthEnum operationAuthEnum = dataRight.operationAuthType();
        PageAuthTypeEnum pageAuthTypeEnum = PageAuthTypeEnum.fromValue(operationAuthEnum.getType());
        ZdPageAuthDTO zdPageAuthDTO = pageAuthService.rightQuery(formCode, pageAuthTypeEnum, CurrentUserFactory.getOrgId());
        if (zdPageAuthDTO == null) {
            throw new BussinessException("3000005");
        }

        String value = operationAuthEnum.getValue();
        List<String> operationAuth = zdPageAuthDTO.getOperationAuthList();
        if (operationAuth.stream().noneMatch(value::equals)) {
            throw new BussinessException("3000005");
        }
        // 假如是查看权限的话，需要设置查看范围
        if (args[0] instanceof ZdFormDataSearchParamDTO) {
            ZdFormDataSearchParamDTO zdFormDataSearchParamDTO = (ZdFormDataSearchParamDTO) args[0];
            zdFormDataSearchParamDTO.setZdPageAuthDTO(zdPageAuthDTO);
        }
    }

    /**
     * 获取表单编码
     *
     * @param args 参数
     * @return 表单编码
     * @since v1.0.0.x
     */
    private static String findFormCode(Object[] args, JoinPoint joinPoint) {
        for (Object arg : args) {
            if (arg instanceof FormCodeSupplier) {
                return ((FormCodeSupplier) arg).getFormCode();
            }
        }
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 从注解里面获取
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (Objects.nonNull(requestParam) && Objects.equals(FormConstant.FORM_CODE, requestParam.value())) {
                return (String) args[i];
            }
        }
        // 从参数名称里面获取
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 参数名称，需要打包支持
            String name = parameter.getName();
            if (Objects.equals(FormConstant.FORM_CODE, name)) {
                return (String) args[i];
            }
        }

        if (args.length == 1 || args[0] instanceof String) {
            return (String) args[0];
        }
        throw new BussinessException("3000004");

    }
}
